import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import 'package:url_launcher/url_launcher.dart';
import 'package:video_player/video_player.dart';

import '../../data/models/villa_model.dart';
import '../../data/models/api_villa_model.dart';
import '../../data/providers/villa_provider.dart';
import '../../routes.dart';
import './calendar_screen.dart'; // Import for CalendarScreen

class VillaDetailScreen extends StatefulWidget {
  final String villaId;

  const VillaDetailScreen({super.key, required this.villaId});

  @override
  State<VillaDetailScreen> createState() => _VillaDetailScreenState();
}

class _VillaDetailScreenState extends State<VillaDetailScreen> {
  int _currentImageIndex = 0;
  VideoPlayerController? _videoController;
  bool _isVideoInitialized = false;
  bool _isVideoPlaying = false;
  final List<Map<String, dynamic>> _mediaItems = [];

  // Google Maps controller
  final Completer<GoogleMapController> _mapController = Completer();
  final Set<Marker> _markers = {};

  @override
  void initState() {
    super.initState();
    // Fetch villa details when the screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        Provider.of<VillaProvider>(
          context,
          listen: false,
        ).fetchVillaDetailsById(widget.villaId);
      }
    });
  }

  @override
  void dispose() {
    _disposeMapController();
    _disposeVideoController();

    // Clear villa detail data when leaving the screen to prevent state contamination
    if (mounted) {
      Provider.of<VillaProvider>(context, listen: false).clearVillaDetailData();
    }

    super.dispose();
  }

  Future<void> _disposeMapController() async {
    // Dispose the GoogleMapController when the widget is disposed.
    if (_mapController.isCompleted) {
      try {
        final GoogleMapController controller = await _mapController.future;
        controller.dispose();
      } catch (e) {
        // Handle or log error if controller disposal fails
        print('Error disposing map controller: $e');
      }
    }
  }

  void _disposeVideoController() {
    if (_videoController != null) {
      _videoController!.dispose();
      _videoController = null;
    }
  }

  Future<void> _initializeVideo(String videoUrl) async {
    try {
      _disposeVideoController(); // Dispose previous controller if any
      _videoController = VideoPlayerController.networkUrl(Uri.parse(videoUrl));

      await _videoController!.initialize();

      if (mounted) {
        setState(() {
          _isVideoInitialized = true;
          _isVideoPlaying = false;
        });
      }

      // Add listener for video completion
      _videoController!.addListener(() {
        if (mounted &&
            _videoController!.value.position >=
                _videoController!.value.duration) {
          setState(() {
            _isVideoPlaying = false;
          });
        }
      });
    } catch (e) {
      print('Error initializing video: $e');
      if (mounted) {
        setState(() {
          _isVideoInitialized = false;
          _isVideoPlaying = false;
        });
      }
    }
  }

  void _toggleVideoPlayback() {
    if (_videoController != null && _isVideoInitialized) {
      setState(() {
        if (_isVideoPlaying) {
          _videoController!.pause();
          _isVideoPlaying = false;
        } else {
          _videoController!.play();
          _isVideoPlaying = true;
        }
      });
    }
  }

  void _onMediaItemTapped(int index) {
    // Dispose current video if switching away from video
    if (_currentImageIndex < _mediaItems.length &&
        _mediaItems[_currentImageIndex]['type'] == 'video') {
      _disposeVideoController();
      setState(() {
        _isVideoInitialized = false;
        _isVideoPlaying = false;
      });
    }

    setState(() {
      _currentImageIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<VillaProvider>(
      builder: (context, villaProvider, child) {
        // Use the new state variables for loading, error, and villa detail
        if (villaProvider.isVillaDetailLoading) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        if (villaProvider.villaDetailError != null) {
          return Scaffold(
            appBar: AppBar(title: const Text('Error')),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(villaProvider.villaDetailError!),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: () {
                      villaProvider.fetchVillaDetailsById(widget.villaId);
                    },
                    child: const Text('Retry'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Go Back'),
                  ),
                ],
              ),
            ),
          );
        }

        final apiVillaData = villaProvider.currentVillaDetail;
        Villa? villaData; // This will be derived from apiVillaData

        if (apiVillaData == null) {
          // This case should ideally be handled by the error state or if fetch hasn't completed
          // Or, if fetch was successful but returned null (e.g. villa not found by API)
          return Scaffold(
            appBar: AppBar(
              title: const Text('Villa Not Found'),
              backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
              foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
              elevation: 0,
            ),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.search_off,
                    size: 64,
                    color: Theme.of(
                      context,
                    ).iconTheme.color?.withValues(alpha: 0.5),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Villa details could not be loaded.',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).textTheme.headlineSmall?.color,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Please try again or go back.',
                    style: TextStyle(
                      color: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      villaProvider.fetchVillaDetailsById(widget.villaId);
                    },
                    child: const Text('Retry Fetch'),
                  ),
                  const SizedBox(height: 12),
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Go Back'),
                  ),
                ],
              ),
            ),
          );
        }

        // If apiVillaData is available, convert it to Villa for UI compatibility
        // The UI primarily uses ApiVilla fields now, but Villa can be a bridge/fallback
        villaData = villaProvider.convertApiVillaToVilla(apiVillaData);
        if (villaData == null) {
          // This means conversion failed, which is unlikely if apiVillaData is valid
          // but handle it as an error.
          return Scaffold(
            appBar: AppBar(title: const Text('Data Error')),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('Could not process villa data.'),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: () {
                      villaProvider.fetchVillaDetailsById(widget.villaId);
                    },
                    child: const Text('Retry'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Go Back'),
                  ),
                ],
              ),
            ),
          );
        }

        return _buildVillaDetailContent(context, villaData, apiVillaData);
      },
    );
  }

  Widget _buildVillaDetailContent(
    BuildContext context,
    Villa villa,
    ApiVilla apiVilla,
  ) {
    // Use data primarily from apiVilla, fallback to villa if necessary
    final String displayAddress =
        apiVilla.address.isNotEmpty
            ? apiVilla.address
            : (villa.location.isNotEmpty ? villa.location : apiVilla.area);
    final double displayPrice =
        apiVilla
            .effectiveWeekdayPrice; // Or another relevant price from ApiVilla
    final String displayDescription =
        apiVilla.desc.isNotEmpty
            ? apiVilla.desc
            : (villa.description ?? 'No description available');

    // Use amenities from ApiVilla, converted to string list by convertApiVillaToVilla
    final List<String> amenities = villa.amenities ?? [];

    _mediaItems.clear();

    // Add profile picture from ApiVilla if available
    if (apiVilla.profilePicture.isNotEmpty) {
      _mediaItems.add({'type': 'image', 'url': apiVilla.profilePicture});
    }

    // Add primary image from ApiVilla if different and not already added
    if (apiVilla.primaryImage.isNotEmpty &&
        !_mediaItems.any((item) => item['url'] == apiVilla.primaryImage)) {
      _mediaItems.add({'type': 'image', 'url': apiVilla.primaryImage});
    }

    // Add additional images from ApiVilla
    for (String img in apiVilla.images) {
      if (img.isNotEmpty && !_mediaItems.any((item) => item['url'] == img)) {
        _mediaItems.add({'type': 'image', 'url': img});
      }
    }

    // Add videos from ApiVilla
    for (String videoUrl in apiVilla.video) {
      if (videoUrl.isNotEmpty) {
        _mediaItems.add({'type': 'video', 'url': videoUrl});
      }
    }

    // Fallback if no media items from ApiVilla, try Villa model's imageUrl
    if (_mediaItems.isEmpty && villa.imageUrl.isNotEmpty) {
      _mediaItems.add({'type': 'image', 'url': villa.imageUrl});
    }
    if (_mediaItems.isEmpty && villa.additionalImages != null) {
      for (String img in villa.additionalImages!) {
        if (img.isNotEmpty && !_mediaItems.any((item) => item['url'] == img)) {
          _mediaItems.add({'type': 'image', 'url': img});
        }
      }
    }

    // Ensure there's at least one media item for display
    if (_mediaItems.isEmpty) {
      _mediaItems.add({
        'type': 'image',
        'url':
            'https://via.placeholder.com/400x250/cccccc/ffffff?text=No+Image',
      });
    }

    // Ensure _currentImageIndex is valid
    if (_currentImageIndex >= _mediaItems.length) {
      _currentImageIndex = 0;
    }

    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            CustomScrollView(
              slivers: [
                // App Bar with back button and actions
                SliverAppBar(
                  leading: IconButton(
                    icon: Icon(
                      Icons.arrow_back_ios,
                      color: Theme.of(context).appBarTheme.iconTheme?.color,
                    ),
                    onPressed: () => Navigator.pop(context),
                  ),
                  actions: [
                    IconButton(
                      icon: Icon(
                        Icons.share_outlined,
                        color: Theme.of(context).appBarTheme.iconTheme?.color,
                      ),
                      onPressed: () {},
                    ),
                    Consumer<VillaProvider>(
                      builder: (context, villaProvider, child) {
                        final isSaved = villaProvider.isVillaSaved(
                          widget.villaId,
                        );
                        return IconButton(
                          icon: Icon(
                            isSaved ? Icons.favorite : Icons.favorite_border,
                            color:
                                isSaved
                                    ? Colors.red
                                    : Theme.of(
                                      context,
                                    ).appBarTheme.iconTheme?.color,
                          ),
                          onPressed: () async {
                            final villa = villaProvider.getVillaByIdExtended(
                              widget.villaId,
                            );
                            if (villa != null) {
                              await villaProvider.toggleSavedStatus(villa);
                            }
                          },
                        );
                      },
                    ),
                  ],
                  backgroundColor:
                      Theme.of(context).appBarTheme.backgroundColor,
                  elevation: 0,
                  pinned: true,
                  expandedHeight: 0,
                  toolbarHeight: 56,
                  title: Text(
                    'Villa Details',
                    style: Theme.of(context).appBarTheme.titleTextStyle,
                  ),
                  centerTitle: true,
                ),

                // Main content
                SliverToBoxAdapter(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Main media display with gallery preview
                      Stack(
                        children: [
                          // Main media display
                          SizedBox(
                            height: 250,
                            width: double.infinity,
                            child: _buildMediaWidget(
                              _mediaItems[_currentImageIndex],
                            ),
                          ),

                          // Price tag
                          Positioned(
                            top: 16,
                            right: 16,
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: Theme.of(
                                  context,
                                ).colorScheme.surface.withValues(alpha: 0.9),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: Theme.of(
                                    context,
                                  ).colorScheme.outline.withValues(alpha: 0.3),
                                ),
                              ),
                              child: Text(
                                '₹${(displayPrice / 1000).toStringAsFixed(0)}K',
                                style: TextStyle(
                                  color:
                                      Theme.of(context).colorScheme.onSurface,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),

                          // Media gallery preview
                          Positioned(
                            bottom: 16,
                            left: 16,
                            right: 16,
                            child: SizedBox(
                              height: 60,
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount: _mediaItems.length,
                                itemBuilder:
                                    (context, index) => GestureDetector(
                                      onTap: () {
                                        _onMediaItemTapped(index);
                                      },
                                      child: Container(
                                        width: 80,
                                        height: 60,
                                        margin: const EdgeInsets.only(right: 8),
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                          border:
                                              _currentImageIndex == index
                                                  ? Border.all(
                                                    color:
                                                        Theme.of(
                                                          context,
                                                        ).colorScheme.primary,
                                                    width: 2,
                                                  )
                                                  : null,
                                        ),
                                        child: ClipRRect(
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                          child: Stack(
                                            children: [
                                              _buildThumbnailWidget(
                                                _mediaItems[index],
                                              ),
                                              // Video play icon overlay
                                              if (_mediaItems[index]['type'] ==
                                                  'video')
                                                Center(
                                                  child: Icon(
                                                    Icons.play_circle_filled,
                                                    color:
                                                        Theme.of(
                                                          context,
                                                        ).colorScheme.onSurface,
                                                    size: 24,
                                                  ),
                                                ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                              ),
                            ),
                          ),
                        ],
                      ),

                      // Price and basic info
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '₹${displayPrice.toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              displayAddress,
                              style: TextStyle(
                                fontSize: 14,
                                color: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.color
                                    ?.withValues(alpha: 0.7),
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Villa capacity and room details
                            ...[
                              // Capacity section
                              Container(
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color:
                                      Theme.of(
                                        context,
                                      ).colorScheme.surfaceContainerHighest,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Capacity & Accommodation',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color:
                                            Theme.of(
                                              context,
                                            ).textTheme.headlineSmall?.color,
                                      ),
                                    ),
                                    const SizedBox(height: 12),
                                    Wrap(
                                      spacing: 16,
                                      runSpacing: 8,
                                      children: [
                                        if (apiVilla.noOfMemberFrom > 0 ||
                                            apiVilla.noOfMemberTo > 0)
                                          _buildCapacityItem(
                                            Icons.people,
                                            '${apiVilla.noOfMemberFrom}-${apiVilla.noOfMemberTo} Guests',
                                          ),
                                        if (apiVilla.noOfRoom > 0)
                                          _buildCapacityItem(
                                            Icons.bedroom_parent,
                                            '${apiVilla.noOfRoom} Rooms',
                                          ),
                                        if (apiVilla.noOfBed > 0)
                                          _buildCapacityItem(
                                            Icons.bed,
                                            '${apiVilla.noOfBed} Beds',
                                          ),
                                        if (apiVilla.noOfwashroom > 0)
                                          _buildCapacityItem(
                                            Icons.bathroom,
                                            '${apiVilla.noOfwashroom} Bathrooms',
                                          ),
                                        if (apiVilla.noOfLivingRoom > 0)
                                          _buildCapacityItem(
                                            Icons.chair,
                                            '${apiVilla.noOfLivingRoom} Living Rooms',
                                          ),
                                        if (apiVilla.noOfAc > 0)
                                          _buildCapacityItem(
                                            Icons.ac_unit,
                                            '${apiVilla.noOfAc} AC Units',
                                          ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 16),

                              // Pricing section
                              Container(
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: Theme.of(context)
                                      .colorScheme
                                      .primaryContainer
                                      .withValues(alpha: 0.3),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Pricing Details',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color:
                                            Theme.of(
                                              context,
                                            ).textTheme.headlineSmall?.color,
                                      ),
                                    ),
                                    const SizedBox(height: 12),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Weekday',
                                              style: TextStyle(
                                                color: Theme.of(context)
                                                    .textTheme
                                                    .bodyMedium
                                                    ?.color
                                                    ?.withValues(alpha: 0.7),
                                                fontSize: 12,
                                              ),
                                            ),
                                            Text(
                                              '₹${apiVilla.effectiveWeekdayPrice.toStringAsFixed(0)}',
                                              style: TextStyle(
                                                fontSize: 18,
                                                fontWeight: FontWeight.bold,
                                                color:
                                                    Theme.of(context)
                                                        .textTheme
                                                        .headlineSmall
                                                        ?.color,
                                              ),
                                            ),
                                            if (apiVilla.weekDayDiscountPrice >
                                                0)
                                              Text(
                                                '₹${apiVilla.weekDayPrice.toStringAsFixed(0)}',
                                                style: TextStyle(
                                                  decoration:
                                                      TextDecoration
                                                          .lineThrough,
                                                  color: Theme.of(context)
                                                      .textTheme
                                                      .bodyMedium
                                                      ?.color
                                                      ?.withValues(alpha: 0.5),
                                                  fontSize: 12,
                                                ),
                                              ),
                                          ],
                                        ),
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.end,
                                          children: [
                                            Text(
                                              'Weekend',
                                              style: TextStyle(
                                                color: Theme.of(context)
                                                    .textTheme
                                                    .bodyMedium
                                                    ?.color
                                                    ?.withValues(alpha: 0.7),
                                                fontSize: 12,
                                              ),
                                            ),
                                            Text(
                                              '₹${apiVilla.effectiveWeekendPrice.toStringAsFixed(0)}',
                                              style: TextStyle(
                                                fontSize: 18,
                                                fontWeight: FontWeight.bold,
                                                color:
                                                    Theme.of(context)
                                                        .textTheme
                                                        .headlineSmall
                                                        ?.color,
                                              ),
                                            ),
                                            if (apiVilla.weekendDiscountPrice >
                                                0)
                                              Text(
                                                '₹${apiVilla.weekendPrice.toStringAsFixed(0)}',
                                                style: TextStyle(
                                                  decoration:
                                                      TextDecoration
                                                          .lineThrough,
                                                  color: Theme.of(context)
                                                      .textTheme
                                                      .bodyMedium
                                                      ?.color
                                                      ?.withValues(alpha: 0.5),
                                                  fontSize: 12,
                                                ),
                                              ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 16),
                            ],

                            const Divider(height: 32),

                            // Property title and description
                            Text(
                              villa.name, // Use actual villa name as title
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color:
                                    Theme.of(
                                      context,
                                    ).textTheme.headlineSmall?.color,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              displayDescription,
                              style: TextStyle(
                                fontSize: 14,
                                color:
                                    Theme.of(
                                      context,
                                    ).textTheme.bodyMedium?.color,
                                height: 1.5,
                              ),
                            ),

                            const SizedBox(height: 24),

                            // Features & Amenities
                            const Text(
                              'Features & Amenities',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Amenities grid
                            if (amenities.isNotEmpty)
                              GridView.count(
                                crossAxisCount: 3,
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                childAspectRatio: 1.5,
                                children:
                                    amenities.map((amenity) {
                                      IconData icon;
                                      switch (amenity.toLowerCase()) {
                                        case 'pool':
                                          icon = Icons.pool;
                                          break;
                                        case 'wifi':
                                          icon = Icons.wifi;
                                          break;
                                        case 'ac':
                                        case 'air conditioning':
                                          icon = Icons.ac_unit;
                                          break;
                                        case 'parking':
                                          icon = Icons.local_parking;
                                          break;
                                        default:
                                          icon = Icons.star; // Fallback icon
                                      }
                                      return Column(
                                        children: [
                                          Icon(
                                            icon,
                                            color:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.primary,
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            amenity,
                                            style: const TextStyle(
                                              fontSize: 12,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ],
                                      );
                                    }).toList(),
                              )
                            else
                              Text(
                                'No amenities listed.',
                                style: TextStyle(
                                  color: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.color
                                      ?.withValues(alpha: 0.7),
                                ),
                              ),

                            // Additional villa details from API
                            ...[
                              const SizedBox(height: 24),

                              // Contact & Services section
                              const Text(
                                'Services & Contact',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),

                              Container(
                                padding: const EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color:
                                      Theme.of(
                                        context,
                                      ).colorScheme.surfaceContainerHighest,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Theme.of(context).colorScheme.outline
                                        .withValues(alpha: 0.3),
                                  ),
                                ),
                                child: Column(
                                  children: [
                                    if (apiVilla.contactNumber.isNotEmpty)
                                      _buildServiceItem(
                                        Icons.phone,
                                        'Contact',
                                        apiVilla.contactNumber,
                                      ),
                                    if (apiVilla.chef)
                                      _buildServiceItem(
                                        Icons.restaurant,
                                        'Chef Service',
                                        'Available',
                                      ),
                                    if (apiVilla.meals)
                                      _buildServiceItem(
                                        Icons.dining,
                                        'Meals',
                                        'Available',
                                      ),
                                    if (apiVilla.complimentary)
                                      _buildServiceItem(
                                        Icons.card_giftcard,
                                        'Complimentary Services',
                                        'Available',
                                      ),
                                    if (apiVilla.noOFcaretaker > 0)
                                      _buildServiceItem(
                                        Icons.person_pin,
                                        'Caretakers',
                                        '${apiVilla.noOFcaretaker} Available',
                                      ),
                                  ],
                                ),
                              ),

                              // Pool details
                              if (apiVilla.isSwimmingPool) ...[
                                const SizedBox(height: 24),
                                const Text(
                                  'Swimming Pool',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .primaryContainer
                                        .withValues(alpha: 0.3),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .primary
                                          .withValues(alpha: 0.3),
                                    ),
                                  ),
                                  child: Column(
                                    children: [
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.pool,
                                            color:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.primary,
                                          ),
                                          const SizedBox(width: 8),
                                          const Text(
                                            'Swimming Pool Available',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                      if (apiVilla
                                          .swimmingPoolMeasument
                                          .isNotEmpty) ...[
                                        const SizedBox(height: 8),
                                        Row(
                                          children: [
                                            Icon(
                                              Icons.straighten,
                                              size: 16,
                                              color: Theme.of(context)
                                                  .iconTheme
                                                  .color
                                                  ?.withValues(alpha: 0.7),
                                            ),
                                            const SizedBox(width: 8),
                                            Text(
                                              'Size: ${apiVilla.swimmingPoolMeasument}',
                                              style: TextStyle(
                                                color: Theme.of(context)
                                                    .textTheme
                                                    .bodyMedium
                                                    ?.color
                                                    ?.withValues(alpha: 0.8),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                      if (apiVilla.isKidSwimmingPool) ...[
                                        const SizedBox(height: 8),
                                        Row(
                                          children: [
                                            Icon(
                                              Icons.child_friendly,
                                              size: 16,
                                              color:
                                                  Colors
                                                      .green, // Keep green for kids-related items
                                            ),
                                            const SizedBox(width: 8),
                                            Text(
                                              'Kids pool available',
                                              style: TextStyle(
                                                color: Theme.of(context)
                                                    .textTheme
                                                    .bodyMedium
                                                    ?.color
                                                    ?.withValues(alpha: 0.8),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ],
                                  ),
                                ),
                              ],

                              // Additional information
                              if (apiVilla.nearby.isNotEmpty ||
                                  apiVilla.direction.isNotEmpty ||
                                  apiVilla.other.isNotEmpty) ...[
                                const SizedBox(height: 24),
                                const Text(
                                  'Additional Information',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                if (apiVilla.nearby.isNotEmpty) ...[
                                  _buildInfoCard(
                                    'Nearby Attractions',
                                    apiVilla.nearby,
                                    Icons.place,
                                  ),
                                  const SizedBox(height: 12),
                                ],
                                if (apiVilla.direction.isNotEmpty) ...[
                                  _buildInfoCard(
                                    'Directions',
                                    apiVilla.direction,
                                    Icons.directions,
                                  ),
                                  const SizedBox(height: 12),
                                ],
                                if (apiVilla.other.isNotEmpty) ...[
                                  _buildInfoCard(
                                    'Other Details',
                                    apiVilla.other,
                                    Icons.info,
                                  ),
                                ],
                              ],
                            ],
                          ],
                        ),
                      ),

                      // Location section
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Location',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 12),

                            // Interactive Map
                            Container(
                              height: 200,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                color:
                                    Theme.of(
                                      context,
                                    ).colorScheme.surfaceContainerHighest,
                                boxShadow: [
                                  BoxShadow(
                                    color: Theme.of(
                                      context,
                                    ).shadowColor.withValues(alpha: 0.1),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(12),
                                child: Stack(
                                  children: [
                                    // Google Map
                                    villa.latitude != null &&
                                            villa.longitude != null
                                        ? GoogleMap(
                                          key: ValueKey(
                                            'google_map_${villa.id}',
                                          ), // Add a unique key
                                          initialCameraPosition: CameraPosition(
                                            target: LatLng(
                                              villa.latitude!,
                                              villa.longitude!,
                                            ),
                                            zoom: 14,
                                          ),
                                          onMapCreated: (
                                            GoogleMapController controller,
                                          ) {
                                            if (!_mapController.isCompleted) {
                                              _mapController.complete(
                                                controller,
                                              );
                                            }

                                            // Add marker for villa location
                                            if (mounted) {
                                              // Check if widget is still mounted
                                              setState(() {
                                                _markers
                                                    .clear(); // Clear previous markers
                                                _markers.add(
                                                  Marker(
                                                    markerId: MarkerId(
                                                      villa.id,
                                                    ),
                                                    position: LatLng(
                                                      villa.latitude!,
                                                      villa.longitude!,
                                                    ),
                                                    infoWindow: InfoWindow(
                                                      title: villa.name,
                                                      snippet: villa.location,
                                                    ),
                                                  ),
                                                );
                                              });
                                            }
                                          },
                                          markers: _markers,
                                          zoomControlsEnabled: false,
                                          mapToolbarEnabled: false,
                                          myLocationButtonEnabled: false,
                                        )
                                        : Center(
                                          child: Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Icon(
                                                Icons.map,
                                                size: 40,
                                                color: Theme.of(context)
                                                    .iconTheme
                                                    .color
                                                    ?.withValues(alpha: 0.5),
                                              ),
                                              const SizedBox(height: 8),
                                              Text(
                                                'Map not available',
                                                style: TextStyle(
                                                  color: Theme.of(context)
                                                      .textTheme
                                                      .bodyMedium
                                                      ?.color
                                                      ?.withValues(alpha: 0.7),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),

                                    // Open in Maps button
                                    if (villa.latitude != null &&
                                        villa.longitude != null)
                                      Positioned(
                                        bottom: 10,
                                        right: 10,
                                        child: ElevatedButton.icon(
                                          onPressed: () async {
                                            final uri = Uri.parse(
                                              'https://www.google.com/maps/search/?api=1&query=${villa.latitude},${villa.longitude}',
                                            );
                                            if (await canLaunchUrl(uri)) {
                                              await launchUrl(
                                                uri,
                                                mode:
                                                    LaunchMode
                                                        .externalApplication,
                                              );
                                            }
                                          },
                                          icon: const Icon(
                                            Icons.directions,
                                            size: 16,
                                          ),
                                          label: const Text('Directions'),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.surface,
                                            foregroundColor:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.primary,
                                            elevation: 2,
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 4,
                                            ),
                                            textStyle: const TextStyle(
                                              fontSize: 12,
                                            ),
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(height: 12),
                            Text(
                              villa
                                  .location, // Use actual villa location description
                              style: TextStyle(
                                fontSize: 14,
                                color: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.color
                                    ?.withValues(alpha: 0.8),
                              ),
                            ),

                            // Add padding at the bottom for the fixed booking button
                            const SizedBox(height: 80),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // Fixed booking button at the bottom
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(
                        context,
                      ).shadowColor.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: ElevatedButton(
                  onPressed: () {
                    // Navigate to CalendarScreen
                    Navigator.of(context).pushNamed(
                      CalendarScreen.routeName,
                      arguments: villa.id, // Pass villaId as argument
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text(
                    'Book Now',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCapacityItem(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: Theme.of(context).colorScheme.primary),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Theme.of(context).textTheme.bodyMedium?.color,
          ),
        ),
      ],
    );
  }

  Widget _buildServiceItem(IconData icon, String title, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Theme.of(context).colorScheme.primary),
          const SizedBox(width: 12),
          Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
          const Spacer(),
          Text(
            value,
            style: TextStyle(
              color: Theme.of(
                context,
              ).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(String title, String content, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 20,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: TextStyle(
              color: Theme.of(
                context,
              ).textTheme.bodyMedium?.color?.withValues(alpha: 0.8),
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMediaWidget(Map<String, dynamic> mediaItem) {
    if (mediaItem['type'] == 'video') {
      return Container(
        color: Theme.of(context).colorScheme.surface,
        child: Stack(
          children: [
            // Video player or thumbnail
            if (_videoController != null &&
                _videoController!.value.isInitialized &&
                _isVideoInitialized)
              AspectRatio(
                aspectRatio: _videoController!.value.aspectRatio,
                child: VideoPlayer(_videoController!),
              )
            else
              Container(
                width: double.infinity,
                height: double.infinity,
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.videocam,
                        size: 48,
                        color: Theme.of(
                          context,
                        ).iconTheme.color?.withValues(alpha: 0.7),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Video Content',
                        style: TextStyle(
                          color: Theme.of(
                            context,
                          ).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            // Video controls overlay
            if (_videoController != null && _isVideoInitialized)
              Center(
                child: GestureDetector(
                  onTap: _toggleVideoPlayback,
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.5),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      _isVideoPlaying ? Icons.pause : Icons.play_arrow,
                      size: 48,
                      color: Colors.white, // Keep white for video controls
                    ),
                  ),
                ),
              )
            else
              // Play button for uninitialized video
              Center(
                child: GestureDetector(
                  onTap: () => _initializeVideo(mediaItem['url']),
                  child: const Icon(
                    Icons.play_circle_filled,
                    size: 64,
                    color: Colors.white, // Keep white for video controls
                  ),
                ),
              ),
          ],
        ),
      );
    } else {
      // For images
      return Image.network(
        mediaItem['url'],
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            child: Center(
              child: Icon(
                Icons.image_not_supported,
                size: 40,
                color: Theme.of(
                  context,
                ).iconTheme.color?.withValues(alpha: 0.5),
              ),
            ),
          );
        },
      );
    }
  }

  Widget _buildThumbnailWidget(Map<String, dynamic> mediaItem) {
    if (mediaItem['type'] == 'video') {
      // For video thumbnails, show a dark overlay
      return Container(
        width: double.infinity,
        height: double.infinity,
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        child: Center(
          child: Icon(
            Icons.videocam,
            size: 20,
            color: Theme.of(context).iconTheme.color?.withValues(alpha: 0.7),
          ),
        ),
      );
    } else {
      // For image thumbnails
      return Image.network(
        mediaItem['url'],
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            child: Center(
              child: Icon(
                Icons.image_not_supported,
                size: 20,
                color: Theme.of(
                  context,
                ).iconTheme.color?.withValues(alpha: 0.5),
              ),
            ),
          );
        },
      );
    }
  }
}
